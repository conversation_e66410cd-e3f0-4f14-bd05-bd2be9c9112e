<template>
  <div class="content-blocks">
    <div v-for="block in blocks" :key="block.id" class="content-block">
      <!-- Текстовый блок -->
      <div
        v-if="block.type === 'text'"
        class="prose dark:prose-invert prose-p:m-0 prose-ul:m-0 prose-h2:my-4 prose-h3:my-3 max-w-none"
        v-html="block.data.content"
      />

      <!-- Блок изображения -->
      <figure v-else-if="block.type === 'image'" class="my-6">
        <img
          :src="block.data.url"
          :alt="block.data.alt"
          class="w-full rounded-lg object-cover"
        />
        <figcaption
          v-if="block.data.caption"
          class="mt-2 text-sm text-[var(--ui-text-muted)] text-center"
        >
          {{ block.data.caption }}
        </figcaption>
      </figure>

      <!-- Блок цитаты -->
      <blockquote
        v-else-if="block.type === 'quote'"
        class="my-6 border-l-4 border-[var(--ui-primary)] bg-[var(--ui-bg-elevated)] p-4 italic"
      >
        <p class="mb-2 text-lg">{{ block.data.text }}</p>
        <cite v-if="block.data.author" class="text-sm text-[var(--ui-text-muted)]">
          — {{ block.data.author }}
        </cite>
      </blockquote>

      <!-- Блок видео -->
      <div v-else-if="block.type === 'video'" class="my-6">
        <div class="aspect-video">
          <iframe
            :src="getEmbedUrl(block.data.url)"
            :title="block.data.title || 'Видео'"
            class="h-full w-full rounded-lg"
            frameborder="0"
            allowfullscreen
          />
        </div>
        <p v-if="block.data.title" class="mt-2 text-sm text-[var(--ui-text-muted)] text-center">
          {{ block.data.title }}
        </p>
      </div>

      <!-- Блок галереи -->
      <div v-else-if="block.type === 'gallery'" class="my-6">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          <img
            v-for="(image, index) in block.data.images"
            :key="index"
            :src="image.url"
            :alt="image.alt"
            class="aspect-square w-full rounded-lg object-cover"
          />
        </div>
      </div>

      <!-- Блок встраиваемого кода -->
      <div
        v-else-if="block.type === 'embed'"
        class="my-6"
        v-html="block.data.code"
      />

      <!-- Неизвестный тип блока -->
      <div v-else class="my-6 rounded-lg bg-red-50 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-200">
        <p class="font-medium">Неизвестный тип блока: {{ block.type }}</p>
        <pre class="mt-2 text-xs">{{ JSON.stringify(block.data, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ContentBlock } from "~/types/content";

defineProps<{
  blocks: ContentBlock[];
}>();

// Функция для преобразования YouTube URL в embed URL
function getEmbedUrl(url: string): string {
  // Проверяем, является ли это YouTube URL
  const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
  const match = url.match(youtubeRegex);
  
  if (match) {
    return `https://www.youtube.com/embed/${match[1]}`;
  }
  
  // Если это не YouTube, возвращаем оригинальный URL
  return url;
}
</script>
