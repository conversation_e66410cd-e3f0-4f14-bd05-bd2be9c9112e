<template>
  <div>
    <!-- Основное изображение статьи -->
    <div v-if="content.data?.image_alt" class="mb-4">
      <img
        :src="getMainImage()"
        :alt="content.data?.title"
        class="mt-0 mb-0 aspect-video w-full rounded-md object-cover"
      />
      <span v-if="content.data?.image_alt" class="mt-1 text-sm text-[var(--ui-text-muted)]">
        Фото: {{ content.data?.image_alt }}
      </span>
    </div>

    <!-- Рендеринг блоков контента -->
    <ContentBlockRenderer v-if="content.data?.blocks" :blocks="content.data.blocks" />

    <!-- Fallback для старого формата -->
    <div
      v-else-if="content.data?.content"
      class="prose dark:prose-invert prose-p:m-0 prose-ul:m-0 prose-h2:my-4 max-w-none"
      v-html="content.data?.content"
    />
  </div>
</template>

<script setup lang="ts">
import type { NewsSingleResponse } from "~/types/news";
import type { ContentSingleResponse } from "~/types/content";

const props = defineProps<{
  news?: NewsSingleResponse;
  content?: ContentSingleResponse;
}>();

// Функция для получения основного изображения
function getMainImage(): string {
  // Для нового формата с блоками - ищем первое изображение в блоках
  if (props.content?.data?.blocks) {
    const firstImageBlock = props.content.data.blocks.find(block => block.type === 'image');
    if (firstImageBlock && firstImageBlock.type === 'image') {
      return firstImageBlock.data.url;
    }
  }

  // Для старого формата
  if (props.news?.data?.image) {
    return props.news.data.image;
  }

  return '';
}
</script>
