<template>
  <div class="space-y-2">
    <NuxtLink
      v-for="(item, index) in news?.data"
      :key="index"
      :to="{ name: 'news-slug', params: { slug: item.slug } }"
      class="block space-y-2 rounded-xl bg-gray-50 p-3 text-left text-sm hover:bg-gray-100 dark:bg-zinc-800 dark:hover:bg-zinc-700"
    >
      <span class="flex gap-2">
        <img
          v-if="item.cover_small"
          :src="item.cover_small"
          :alt="item.title"
          class="object-fit h-7 w-7 rounded-sm"
        />
        <h3 class="leading-none text-black dark:text-white">
          {{ item.title }}
        </h3>
      </span>
      <p class="line-clamp-3 text-[var(--ui-text-muted)]">
        {{ item.description }}
      </p>
      <NuxtTime class="text-[var(--ui-text-muted)] text-sm" :datetime="item.published_at" relative />
    </NuxtLink>

    <UButton to="/news" size="sm" block variant="soft"> Новости</UButton>
  </div>
</template>

<script setup lang="ts">
import type { NewsResponse } from "~/types/news";

const props = defineProps({
  limit: {
    type: Number,
    default: 5
  }
});

const client = useSanctumClient();

const { data: news } = await useAsyncData<NewsResponse>("news", () =>
  client("/news", {
    params: {
      limit: props.limit
    }
  })
);
</script>
