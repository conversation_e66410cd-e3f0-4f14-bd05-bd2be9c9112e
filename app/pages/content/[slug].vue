<template>
  <UContainer class="mt-4">
    <div class="flex items-center gap-2">
      <UButton to="/content" size="xs" color="neutral" variant="outline" icon="i-lucide-arrow-left"
        >Назад</UButton
      >
      <NuxtTime
        v-if="content.data?.published_at"
        :datetime="content.data?.published_at"
        date-style="long"
        time-style="short"
        class="text-sm text-[var(--ui-text-muted)]"
      />
    </div>

    <UPageCard :title="content.data?.title" variant="naked" orientation="horizontal" class="mt-3 mb-4">
      <template #title>
        <h1 class="text-2xl font-bold">{{ content.data?.title }}</h1>
      </template>
    </UPageCard>

    <div class="flex flex-col gap-4 lg:flex-row">
      <div class="w-full">
        <NewsPost :content="content" />
      </div>
      <div class="w-full space-y-4 lg:w-110">
        <div class="sticky top-2 space-y-2">
          <div v-for="post in posts.data" :key="post.slug">
            <CardItemSmall :item="post" />
          </div>

          <!--          <UButton to="/news" size="sm" block variant="soft"> Новости </UButton> -->
        </div>
      </div>
    </div>

    <h3 class="my-4 font-semibold text-(--ui-text-highlighted)">Похожие материалы</h3>
    <div class="grid w-full grid-cols-1 gap-4 md:grid-cols-3">
      <UBlogPost
        v-for="(item, index) in relatedContent"
        :key="index"
        :to="{ name: 'content-slug', params: { slug: item.slug } }"
        :title="item.title"
        :description="item.description"
        :image="getItemImage(item)"
        :date="item.published_at"
      />
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import type { ContentSingleResponse, ContentItem } from "~/types/content";
import type { PostsResponse } from "~/types/listing";

const route = useRoute();
const client = useSanctumClient();

const { data: content } = await useAsyncData<ContentSingleResponse>(`content:${route.params.slug}`, () =>
  client(`/content/${route.params.slug}`)
);

const { data: posts } = await useAsyncData<PostsResponse>("posts:limit:5", () =>
  client("/posts?limit=5")
);

if (!content.value?.data) {
  throw createError({ statusCode: 404, fatal: true });
}

// Мок данных для похожих материалов (пока нет API)
const relatedContent = computed(() => {
  // Здесь можно будет загрузить похожие материалы из той же категории
  return [];
});

// Функция для получения изображения элемента
function getItemImage(item: ContentItem): string {
  // Ищем первое изображение в блоках
  if (item.blocks) {
    const firstImageBlock = item.blocks.find(block => block.type === 'image');
    if (firstImageBlock && firstImageBlock.type === 'image') {
      return firstImageBlock.data.url;
    }
  }
  return '';
}

useSchemaOrg([
  defineBreadcrumb({
    itemListElement: [
      { name: "Главная", item: "/" },
      { name: content.value.data.category.name, item: "/content" },
      { name: content.value.data.title }
    ]
  }),
  defineArticle({
    headline: content.value?.data?.meta_title || content.value?.data?.title,
    description: content.value?.data?.meta_description || content.value?.data?.description,
    image: getMainContentImage(),
    datePublished: content.value.data?.published_at,
    dateModified: content.value.data?.updated_at,
    author: {
      name: "Паша Прав",
      url: "https://pasha-prav.ru/",
      image: "/images/pasha.jpg"
    }
  })
]);

// Функция для получения основного изображения для SEO
function getMainContentImage(): string {
  if (content.value?.data?.blocks) {
    const firstImageBlock = content.value.data.blocks.find(block => block.type === 'image');
    if (firstImageBlock && firstImageBlock.type === 'image') {
      return firstImageBlock.data.url;
    }
  }
  return '';
}

useSeoMeta({
  title: content.value?.data?.meta_title || content.value?.data?.title,
  ogTitle: content.value?.data?.meta_title || content.value?.data?.title,
  description: content.value?.data?.meta_description || content.value?.data?.description,
  ogDescription: content.value?.data?.meta_description || content.value?.data?.description,
  ogImage: getMainContentImage(),
  ogType: "article",
  ogLocale: "ru"
});
</script>
